#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to examine the BGL dataset format
"""

import os

def examine_bgl_log(file_path, num_lines=10):
    """Examine the first few lines of the BGL log file"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            print(f"Examining first {num_lines} lines of {file_path}")
            print("=" * 80)
            
            for i in range(num_lines):
                line = f.readline()
                if not line:
                    print(f"End of file reached at line {i}")
                    break
                
                # Print line number and content
                print(f"Line {i+1:2d}: {line.rstrip()}")
                
                # Analyze the structure
                if i == 0:
                    parts = line.strip().split()
                    print(f"         First line has {len(parts)} parts")
                    if len(parts) > 0:
                        print(f"         First character: '{line[0]}'")
                        if line[0] == '-':
                            print("         This appears to be a non-alert message")
                        else:
                            print("         This appears to be an alert message")
            
            print("=" * 80)
            
    except Exception as e:
        print(f"Error reading file: {e}")

if __name__ == "__main__":
    bgl_path = "/home/<USER>/Documents/log-anomaly/datasets/BGL/BGL.log"
    examine_bgl_log(bgl_path, 10)
